// PictureInPicture.scss - Scoped styles for Picture-in-Picture component
// This file contains all styles for the PiP component and is scoped to avoid affecting other components

// Scope all styles to PiP container to avoid affecting main website
.pip-container {
  // Reset styles only within PiP container
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  // Container styles - body-like styles only for PiP container
  background: #000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow: hidden;
  width: 100vw;
  height: 100vh;
  color: white;
  padding: 2vmin;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.pip-main-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pip-grid-container {
  width: 100%;
  height: 100%;
  display: grid;
  gap: 1vmin;
  box-sizing: border-box;
}

// Dynamic grid layouts based on participant count
// Solo layouts (1 participant + control)
.pip-grid-solo {
  &.control-top {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }

  &.control-bottom {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr auto;
  }
}

// Two participant layouts (2 participants + control) - Vertical stack
.pip-grid-two {
  &.control-top {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr 1fr;
  }

  &.control-bottom {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr 1fr auto;
  }
}

// Three participant layouts (3 participants + control) - 1 large top, 2 small bottom
.pip-grid-three {
  &.control-top {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto 2fr 1fr;
  }

  &.control-bottom {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 2fr 1fr auto;
  }
}

// Four participant layouts (4 participants + control) - 1 large top, 3 small bottom
.pip-grid-four {
  &.control-top {
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: auto 2fr 1fr;
  }

  &.control-bottom {
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 2fr 1fr auto;
  }
}

// SCREEN SHARE LAYOUTS
// Screen share solo layouts (screen share + local + control) - Equal sized tiles
.pip-grid-screenshare-solo {
  &.control-top {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr 1fr;
  }

  &.control-bottom {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr 1fr auto;
  }
}

// Screen share two layouts (screen share + local + 1 remote + control)
.pip-grid-screenshare-two {
  &.control-top {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto 2fr 1fr;
  }

  &.control-bottom {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 2fr 1fr auto;
  }
}

// Screen share three layouts (screen share + local + 2 remotes + control)
.pip-grid-screenshare-three {
  &.control-top {
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: auto 2fr 1fr;
  }

  &.control-bottom {
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 2fr 1fr auto;
  }
}

// Tile base styles
.pip-tile {
  border: none;
  border-radius: 2vmin;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

// Control tile styles
.pip-control-tile {
  background-color: rgba(42, 42, 42, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 2.5vmin;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 16vmin;
  height: 16vmin;
  grid-column: 1 / -1;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  padding: 1.5vmin;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.pip-control-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 0;

  // Individual LiveKit component styling for PiP
  .pip-control-buttons {
    display: flex !important;
    gap: 2vmin !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    height: 100% !important;
    padding: 0 !important;
    margin: 0 !important;

    // Prevent any layout shifts or flickering
    contain: layout style paint !important;
    isolation: isolate !important;
  }

  // Style individual control buttons
  .pip-control-button {
    width: 12vmin !important;
    height: 8vmin !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-radius: 1.2vmin !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    transition: background-color 0.2s ease, border-color 0.2s ease !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 !important;
    padding: 0 !important;
    position: relative !important;
    overflow: hidden !important;

    // Prevent flickering and re-rendering issues
    will-change: background-color, border-color !important;
    backface-visibility: hidden !important;
    -webkit-backface-visibility: hidden !important;
    transform: translateZ(0) !important; // Force hardware acceleration
    -webkit-transform: translateZ(0) !important;

    svg {
      color: rgba(255, 255, 255, 0.8) !important;
      transition: color 0.2s ease !important;
      will-change: color !important;
      transform: translateZ(0) scale(0.7) !important; // Scale down SVG and force hardware acceleration
    }

    &:hover {
      background-color: rgba(255, 255, 255, 0.2) !important;
      transform: translateZ(0) !important; // Maintain hardware acceleration
    }

    &[data-lk-enabled="true"] {
      background-color: #1e8cfa !important;
      border-color: rgba(255, 255, 255, 0.2) !important;

      svg {
        color: #fff !important;
      }
    }

    &[data-lk-enabled="false"] {
      background-color: rgba(255, 255, 255, 0.1) !important;

      svg {
        color: rgba(255, 255, 255, 0.8) !important;
      }
    }

    // Prevent any animation conflicts and flickering
    &:focus,
    &:active {
      outline: none !important;
      box-shadow: none !important;
      transform: translateZ(0) !important;
    }

    // Hide any text labels and unwanted elements
    span,
    .lk-button-text {
      display: none !important;
    }

    // Ensure stable rendering
    &::before,
    &::after {
      display: none !important;
    }
  }

  // Participants count button specific styling
  .pip-participants-button {
    background-color: rgba(30, 140, 250, 0.9) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
    color: #fff !important;
    font-size: 2.5vmin !important;
    font-weight: 600 !important;

    &:hover {
      background-color: rgba(30, 140, 250, 1) !important;
      transform: translateZ(0) !important;
    }

    &:focus,
    &:active {
      outline: none !important;
      box-shadow: none !important;
      transform: translateZ(0) !important;
    }
  }

  // Disconnect button specific styling
  .pip-disconnect-button {
    background-color: rgba(255, 59, 48, 0.9) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;

    svg {
      color: #fff !important;
      transform: translateZ(0) scale(0.7) !important;
    }

    &:hover {
      background-color: #ff1a1a !important;
    }

    // Ensure icon is visible and centered
    .anticon {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;

      svg {
        color: #fff !important;
        transform: translateZ(0) scale(0.7) !important;
      }
    }

    // Hide any text content
    span:not(.anticon) {
      display: none !important;
    }
  }
}

// Old custom control styles removed - now using LiveKit ControlBar

// Main tile positioning based on layout
.pip-tile-main {
  background-color: #1e1e1e;
  border-radius: 2.5vmin;
  transition: box-shadow 0.3s ease, border 0.3s ease;
}

// Screen share tile styles
.pip-tile-screenshare {
  background-color: #1e1e1e;
  border-radius: 2.5vmin;
  transition: box-shadow 0.3s ease, border 0.3s ease;
  grid-column: 1 / -1;
  grid-row: 2;
}

// Screen share tile positioning for different layouts
.pip-grid-screenshare-solo,
.pip-grid-screenshare-two,
.pip-grid-screenshare-three {
  &.control-top .pip-tile-screenshare {
    grid-column: 1 / -1;
    grid-row: 2;
  }

  &.control-bottom .pip-tile-screenshare {
    grid-column: 1 / -1;
    grid-row: 1;
  }
}

// Local participant tile when screen sharing
.pip-tile-local {
  background-color: #1e1e1e;
  border-radius: 2vmin;
  transition: box-shadow 0.3s ease, border 0.3s ease;
}

// Main tile positioning for different layouts
.pip-grid-solo {
  &.control-top .pip-tile-main {
    grid-column: 1;
    grid-row: 2;
  }

  &.control-bottom .pip-tile-main {
    grid-column: 1;
    grid-row: 1;
  }
}

.pip-grid-two {
  &.control-top .pip-tile-main {
    grid-column: 1;
    grid-row: 2;
  }

  &.control-bottom .pip-tile-main {
    grid-column: 1;
    grid-row: 1;
  }
}

.pip-grid-three,
.pip-grid-four {
  &.control-top .pip-tile-main {
    grid-column: 1 / -1;
    grid-row: 2;
  }

  &.control-bottom .pip-tile-main {
    grid-column: 1 / -1;
    grid-row: 1;
  }
}

// Speaking animation styles
.pip-tile-speaking {
  box-shadow: 0 0 0 2px #1e8cfa, 0 0 12px rgba(30, 140, 250, 0.3) !important;
  animation: pip-speaking-pulse 3s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  position: relative;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, box-shadow;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 2.5vmin;
    background: radial-gradient(circle at center, rgba(30, 140, 250, 0.2) 0%, transparent 70%);
    animation: pip-speaking-glow 3s ease-in-out infinite;
    z-index: 0;
    transition: opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity, transform;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 2.5vmin;
    background: radial-gradient(circle at center, rgba(30, 140, 250, 0.1) 0%, transparent 60%);
    animation: pip-speaking-delay 3s ease-in-out infinite;
    z-index: 0;
    opacity: 0;
    transition: opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity, transform;
  }
}

// Keyframe animations
@keyframes pip-speaking-pulse {
  0% {
    box-shadow: 0 0 0 2px #1e8cfa, 0 0 12px rgba(30, 140, 250, 0.3);
    transform: scale(1);
    opacity: 1;
  }
  15% {
    box-shadow: 0 0 0 2.5px #1e8cfa, 0 0 16px rgba(30, 140, 250, 0.4);
    transform: scale(1.003);
    opacity: 1;
  }
  30% {
    box-shadow: 0 0 0 3px #1e8cfa, 0 0 20px rgba(30, 140, 250, 0.5);
    transform: scale(1.006);
    opacity: 1;
  }
  45% {
    box-shadow: 0 0 0 2.5px #1e8cfa, 0 0 16px rgba(30, 140, 250, 0.4);
    transform: scale(1.003);
    opacity: 1;
  }
  60% {
    box-shadow: 0 0 0 2px #1e8cfa, 0 0 12px rgba(30, 140, 250, 0.3);
    transform: scale(1);
    opacity: 0.95;
  }
  100% {
    box-shadow: 0 0 0 2px #1e8cfa, 0 0 12px rgba(30, 140, 250, 0.3);
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pip-speaking-glow {
  0% {
    opacity: 0.15;
    transform: scale(0.98);
  }
  30% {
    opacity: 0.4;
    transform: scale(1.02);
  }
  60% {
    opacity: 0.15;
    transform: scale(0.98);
  }
  100% {
    opacity: 0.15;
    transform: scale(0.98);
  }
}

@keyframes pip-speaking-delay {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  20% {
    opacity: 0;
    transform: scale(0.95);
  }
  40% {
    opacity: 0.3;
    transform: scale(1.03);
  }
  60% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 0;
    transform: scale(0.95);
  }
}

// Small tile positioning for different layouts
.pip-tile-small {
  aspect-ratio: 16 / 9;
  border-radius: 2vmin;
  background-color: #1e1e1e;
  transition: box-shadow 0.3s ease, border 0.3s ease;
}

// Two participant layout - second participant positioning
.pip-grid-two {
  &.control-top .pip-tile-small {
    grid-row: 3;
    grid-column: 1;
  }

  &.control-bottom .pip-tile-small {
    grid-row: 2;
    grid-column: 1;
  }
}

// Three participant layout - remote participants positioning
.pip-grid-three {
  &.control-top .pip-tile-small {
    grid-row: 3;
  }

  &.control-bottom .pip-tile-small {
    grid-row: 2;
  }
}

// Four participant layout - remote participants positioning
.pip-grid-four {
  &.control-top .pip-tile-small {
    grid-row: 3;
  }

  &.control-bottom .pip-tile-small {
    grid-row: 2;
  }
}

// Screen share layouts - small tile positioning
// Screen share solo layout - local participant positioning
.pip-grid-screenshare-solo {
  &.control-top .pip-tile-small {
    grid-row: 3;
    grid-column: 1;
  }

  &.control-bottom .pip-tile-small {
    grid-row: 2;
    grid-column: 1;
  }
}

// Screen share two layout - local + 1 remote positioning
.pip-grid-screenshare-two {
  &.control-top .pip-tile-small {
    grid-row: 3;
  }

  &.control-bottom .pip-tile-small {
    grid-row: 2;
  }
}

// Screen share three layout - local + 2 remotes positioning
.pip-grid-screenshare-three {
  &.control-top .pip-tile-small {
    grid-row: 3;
  }

  &.control-bottom .pip-tile-small {
    grid-row: 2;
  }
}

// Individual tile styles
.pip-tile-1,
.pip-tile-2,
.pip-tile-3 {
  background-color: #1e1e1e;
  border-radius: 2vmin;
}

.pip-tile-center-dot {
  width: 2vmin;
  height: 2vmin;
  background-color: #ff69b4;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// Video container styles
.pip-video-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 2vmin;

  video {
    width: 100% !important;
    height: 100% !important;
    object-fit: contain !important;
    aspect-ratio: 16 / 9 !important;
    max-width: 100%;
    max-height: 100%;
    border-radius: 2vmin !important;
  }
}

// Participant tile custom styles
.pip-tile-pip-custom .lk-participant-metadata {
  display: none !important;
}

.pip-tile-participant-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.pip-tile-overlay {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  pointer-events: none;
  padding: 0.25em 0.4em 0.35em 0.4em;
  box-sizing: border-box;
}

.pip-tile-overlay-left {
  display: flex;
  align-items: center;
  gap: 0.35em;
  background: rgba(20, 20, 20, 0.72);
  border-radius: 0.35em;
  padding: 0.12em 0.5em 0.12em 0.3em;
  font-size: 0.85em;
  color: #fff;
  pointer-events: auto;
  min-height: 1.7em;

  svg {
    width: 1.1em;
    height: 1.1em;
    margin-right: 0.1em;
  }
}

.pip-tile-overlay-right {
  display: flex;
  align-items: center;
  background: rgba(20, 20, 20, 0.72);
  border-radius: 0.35em;
  padding: 0.12em 0.4em;
  font-size: 0.85em;
  color: #fff;
  pointer-events: auto;
  min-height: 1.7em;

  svg {
    width: 1.1em;
    height: 1.1em;
  }
}

// Avatar styles
.pip-tile-avatar-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  pointer-events: none;
}

.pip-tile-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #fff;
  font-weight: 600;
  font-size: 8vmin;
  background: #7C4DFF;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.12);
  user-select: none;
  pointer-events: auto;
  transition: width 0.2s, height 0.2s, font-size 0.2s;
  aspect-ratio: 1 / 1;
  width: 25vmin;
  height: 25vmin;

  &.pip-tile-avatar-small {
    font-size: 4vmin !important;
    width: 12vmin !important;
    height: 12vmin !important;
  }
}

// Corner pulse animation for speaking indicators
@keyframes pip-corner-pulse {
  0% {
    opacity: 0.6;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.6;
    transform: scale(0.8);
  }
}

// Floating Screen Capture Button Styles
.pip-screen-capture-button {
  position: absolute;
  top: 1vmin;
  right: 1vmin;
  z-index: 10;
  opacity: 0.9;

  // Override ScreenCaptureButton styles for PiP
  .settings-menu-item {
    background: rgba(0, 0, 0, 0.7);
    border-radius: 1.5vmin;
    padding: 1vmin;
    cursor: pointer;
    transition: transform 0.1s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    min-width: auto;
    width: auto;
    height: auto;
    display: flex;
    align-items: center;
    justify-content: center;

    &:active {
      transform: scale(0.95);
      background: rgba(0, 0, 0, 0.95);
    }
  }

  .settings-menu-inner-icon {
    margin: 0;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
      width: 3vmin;
      height: 3vmin;
      color: #fff;
    }
  }

  .settings-menu-inner-text {
    display: none; // Hide text in PiP to keep button small
  }
}

// Responsive media queries for different PiP sizes
@media (max-width: 400px) {
  .pip-tile-overlay-left,
  .pip-tile-overlay-right {
    font-size: 4vmin;
    padding: 0.8vmin 1.5vmin;
    border-radius: 1.2vmin;

    svg {
      width: 5vmin;
      height: 5vmin;
    }
  }

  .pip-tile-avatar {
    font-size: 6vmin;
    width: 20vmin;
    height: 20vmin;
  }

  .pip-screen-capture-button {
    top: 2vmin;
    right: 2vmin;

    .settings-menu-inner-icon svg {
      width: 4vmin;
      height: 4vmin;
    }
  }
}

@media (max-width: 300px) {
  .pip-tile-overlay-left,
  .pip-tile-overlay-right {
    font-size: 3.5vmin;
    padding: 0.6vmin 1.2vmin;
    border-radius: 1vmin;

    svg {
      width: 4.5vmin;
      height: 4.5vmin;
    }
  }

  .pip-tile-avatar {
    font-size: 5.5vmin;
    width: 18vmin;
    height: 18vmin;
  }

  .pip-screen-capture-button .settings-menu-inner-icon svg {
    width: 5vmin;
    height: 5vmin;
  }
}

@media (max-width: 200px) {
  .pip-tile-overlay-left,
  .pip-tile-overlay-right {
    font-size: 3vmin;
    padding: 0.5vmin 1vmin;
    border-radius: 0.8vmin;
    gap: 0.5vmin;

    svg {
      width: 4vmin;
      height: 4vmin;
    }
  }

  .pip-tile-avatar {
    font-size: 5vmin;
    width: 15vmin;
    height: 15vmin;
  }
}

@media (max-width: 100px) {
  .pip-tile-overlay-left,
  .pip-tile-overlay-right {
    font-size: 2.5vmin;
    padding: 0.4vmin 0.8vmin;
    border-radius: 0.6vmin;

    svg {
      width: 3vmin;
      height: 3vmin;
    }
  }

  .pip-tile-avatar {
    font-size: 4.5vmin;
    width: 12vmin;
    height: 12vmin;
  }
}

// Height-based media queries
@media (max-height: 200px) {
  .pip-tile-overlay-left,
  .pip-tile-overlay-right {
    font-size: 3vmin;
    padding: 0.5vmin 1vmin;
  }

  .pip-tile-avatar {
    font-size: 5vmin;
    width: 15vmin;
    height: 15vmin;
  }
}

@media (max-height: 100px) {
  .pip-tile-overlay-left,
  .pip-tile-overlay-right {
    font-size: 2.5vmin;
    padding: 0.4vmin 0.8vmin;
  }

  .pip-tile-avatar {
    font-size: 4.5vmin;
    width: 12vmin;
    height: 12vmin;
  }
}



// Expanded View Container - Match main container padding
.pip-expanded-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #000;
  border-radius: 2vmin;
  overflow: hidden;
  box-sizing: border-box;
}

// Expanded View Header
.pip-expanded-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2vmin;
  background: rgba(42, 42, 42, 0.95);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  min-height: 8vmin;
  flex-shrink: 0;
}

// Navigation Buttons
.pip-nav-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.5vmin;
  color: white;
  padding: 1.5vmin 3vmin;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 2.5vmin;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 8vmin;
  min-height: 5vmin;

  &:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    background: rgba(255, 255, 255, 0.15);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: rgba(255, 255, 255, 0.05);
  }
}

// Pagination Container
.pip-pagination {
  display: flex;
  align-items: center;
  gap: 2vmin;
}

// Page Info
.pip-page-info {
  color: rgba(255, 255, 255, 0.8);
  font-size: 2.5vmin;
  font-weight: 500;
  min-width: 12vmin;
  text-align: center;
}

// Expanded Grid Container - Match main layout exactly
.pip-expanded-grid {
  flex: 1;
  display: grid;
  gap: 1vmin; // Same as pip-grid-container
  padding: 0; // No padding, same as pip-grid-container
  overflow: hidden;
  box-sizing: border-box;
}

// Expanded Grid Layouts
.pip-expanded-grid-1 {
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
}

.pip-expanded-grid-4 {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

.pip-expanded-grid-6 {
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

.pip-expanded-grid-9 {
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr;
}

// Expanded Tile Styles - Match main layout exactly
.pip-tile-expanded {
  background-color: #1e1e1e;
  border-radius: 2vmin;
  transition: box-shadow 0.3s ease, border 0.3s ease;
  aspect-ratio: 16 / 9;
  min-height: 0;
  overflow: hidden;

  // Apply same styles as pip-tile-small
  border-radius: 2vmin;
  background-color: #1e1e1e;
  transition: box-shadow 0.3s ease, border 0.3s ease;
}

// Expanded Avatar Styles - Match small avatar exactly
.pip-tile-avatar-expanded {
  // Use same sizing as pip-tile-avatar-small
  font-size: 4vmin !important;
  width: 12vmin !important;
  height: 12vmin !important;
}

// Responsive adjustments for expanded view
@media (max-width: 400px) {
  .pip-count-indicator {
    width: 8vmin;
    height: 8vmin;
    font-size: 4vmin;
    top: 2vmin;
    right: 2vmin;
  }

  .pip-expanded-header {
    padding: 3vmin 2vmin;
    min-height: 12vmin;
  }

  .pip-nav-button {
    padding: 2vmin 4vmin;
    font-size: 3.5vmin;
    min-width: 10vmin;
    min-height: 7vmin;
  }

  .pip-page-info {
    font-size: 3.5vmin;
    min-width: 15vmin;
  }

  .pip-expanded-grid {
    gap: 1vmin; // Keep same as main layout
    padding: 0; // Keep same as main layout
  }

  .pip-tile-avatar-expanded {
    // Keep same as pip-tile-avatar-small in responsive
    font-size: 4vmin !important;
    width: 12vmin !important;
    height: 12vmin !important;
  }
}

@media (max-width: 300px) {
  .pip-count-indicator {
    width: 10vmin;
    height: 10vmin;
    font-size: 5vmin;
  }

  .pip-nav-button {
    padding: 2.5vmin 5vmin;
    font-size: 4vmin;
    min-width: 12vmin;
    min-height: 8vmin;
  }

  .pip-page-info {
    font-size: 4vmin;
    min-width: 18vmin;
  }

  .pip-tile-avatar-expanded {
    // Keep same as pip-tile-avatar-small in responsive
    font-size: 4vmin !important;
    width: 12vmin !important;
    height: 12vmin !important;
  }
}
